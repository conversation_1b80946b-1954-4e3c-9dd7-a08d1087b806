#!/usr/bin/python
# -*- coding: UTF-8 -*-

# 引入库
from machine import UART, Timer
from Maix import GPIO, freq
import time, gc, sensor, image, network, socket, uos, ujson
from fpioa_manager import fm

print("启动中，请稍后...\r\n")

# 初始化IO##########################################################################################
def lampAndButton(boolean):
    # 标记全局变量
    global LED0,LED1,LED2,KEY0,PowerKey,lampAndButtonState
    # 判断布尔条件
    if boolean == False:
        try:
            print(">>> 初始化IO...")
            # 初始化LED管脚，从左到右
            # 第一个(WIFI)
            fm.register(12, fm.fpioa.GPIO5)
            LED0 = GPIO(GPIO.GPIO5, GPIO.OUT)
            # 第二个(服务器)
            fm.register(11, fm.fpioa.GPIO4)
            LED1 = GPIO(GPIO.GPIO4, GPIO.OUT)
            # 第三个(系统状态)
            fm.register(13, fm.fpioa.GPIO3)
            LED2 = GPIO(GPIO.GPIO3, GPIO.OUT)
            # 校准按钮
            fm.register(14, fm.fpioa.GPIO6)
            KEY0 = GPIO(GPIO.GPIO6, GPIO.IN)
            # 电源按钮
            fm.register(15, fm.fpioa.GPIO7)
            PowerKey = GPIO(GPIO.GPIO7, GPIO.OUT)
            #LED控制
            LedState = 0
            #LED控制计数
            counter = 0
            while counter <= 5:
                counter += 1
                if LedState == 0:
                    LED0.value(0)
                    LED1.value(0)
                    LED2.value(0)
                    LedState = 1
                else:
                    LED0.value(1)
                    LED1.value(1)
                    LED2.value(1)
                    LedState = 0
                time.sleep_ms(200)
            print("初始化IO成功！！！\r\n")
            # 初始化IO成功
            lampAndButton(True)
        except Exception as e:
            print(">>> 初始化IO发生错误：%s\r\n" % e)
            # 标记状态
            lampAndButtonState = 0
    else:
        # 标记状态
        lampAndButtonState = 1

# 初始化摄像头#######################################################################################
def cameraReset(boolean):
    # 标记全局变量
    global cameraResetState
    if boolean == False:
        try:
            print(">>> 初始化摄像头....")
            # 配置摄像头参数
            sensor.reset(freq=23000000, set_regs=True, dual_buff=True)
            #sensor.set_pixformat(sensor.RGB565)
            sensor.set_pixformat(sensor.GRAYSCALE)
            sensor.set_framesize(sensor.QVGA)
            # 定义IED接收图像质量
            #sensor.set_jb_quality(95)
            # 对比度
            #sensor.set_contrast(-3)
            # 亮度
            #sensor.set_brightness(-3)
            # 饱和度
            #sensor.set_saturation(-3)
            # 自动增益
            #sensor.set_auto_gain(False, gain_db_ceiling = 1.0)
            sensor.set_auto_gain(False)
            # 白平衡
            #sensor.set_auto_whitebal(False)
            # 跳过帧率让摄像头稳定
            sensor.skip_frames(time = 2000)
            print("初始化摄像头成功！！！\r\n")
            # LED控制
            LED0.value(0)
            # 标记状态
            cameraResetState = 1
        except Exception as e:
            print("初始化摄像头发生错误：%s\r\n" % e)
            #LED控制
            LED0.value(1)
            # 标记状态
            cameraResetState = 0

# 校准逻辑函数#######################################################################################
# 校准数据[Xcenter(中心X),Ycenter(中心Y),XSmall(最小X),XLarge(最大X),YSmall(最小Y),YLarge(最大Y)]
calibrationArray = [0,0,0,0,0,0]
# 校准步骤
calibrationStep = 0
# 校准步骤计数（主要用于检测校准光是否稳定，累加50个为完成）
calibrationStepCount = 0
# 校准数据状态
calibrationState = 0
# 错误计数
calibrationWErrorCount = 0

def calibration(boolean):
    # 标定全局变量
    global calibrationArray, calibrationJson, calibrationState, calibrationWErrorCount, factoryCalibration
    # 赋值信息
    calibrationJson = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})

    # 写入校准数据到文件
    if boolean == True:
        try:
            print("正在写入校准数据到FLASH，写入信息为：%s\r\n" % calibrationJson)
            time.sleep_ms(1000)
            filesW = open('./calibration.json', 'w',encoding = 'utf-8' )
            # 尝试写入
            filesW.write(calibrationJson)
            time.sleep_ms(200)
            # 关闭写入文件
            filesW.close()
            # 标记状态
            calibrationState = 1
        except Exception as e:
            print("写入校准数据文件发生错误：%s\r\n" % e)
            # 标记状态
            calibrationState = 0

    else:
        # 读取校准文件,如果发生错误将进行初始化.
        try:
            print('>>> 读取校准数据文件"calibration.json"，开始寻找文件...')
            # 尝试读取校准数据文件
            filesR = open('./calibration.json', 'r',encoding = 'utf-8' )
            calibrationJson = filesR.readline()
            printInfo = calibrationJson,len(calibrationJson), id(calibrationJson)
            print("校准数据文件：%s，文件长度：%i，内存地址：%i\r\n" % printInfo)
            # 判断读文件内容长度，如果空就进入写入模式
            if len(calibrationJson) == 0:
                print("读取校准数据文件错误，进行文件初始化...\r\n")
                # 关闭读取文件
                filesR.close()
                # 删除文件
                uos.remove('./calibration.json')
                print("删除校准文件成功！！！\r\n")
                # 标记状态
                calibrationState = 0
            else:
                # 转化为对象
                calibrationJson = ujson.loads(calibrationJson)
                if calibrationJson["Xcenter"] == 0 and calibrationJson["XSmall"] == 0:
                    # 没有校准数据，直接进入校准报错。
                    calibrationState = 2
                else:
                    # 标记状态
                    calibrationState = 1
            # 关闭打开的文件
            filesR.close()
        except Exception as e:
            print("读取校准数据文件发生错误：%s\r\n" % e)
            # 标记状态
            calibrationState = 0

# 初始化WiFi硬件#####################################################################################
# 初始化WiFi硬件状态
initWiFiState = 0
# 初始化结果（如果完成了首次初始化改变为：True）
initWiFiresult = False
def initWiFi():
    global nic, initWiFiState, initWiFiresult
    try:
        print(">>> 初始化WiFi硬件...")
        if initWiFiresult == False:
            # 初始化M1W使能
            fm.register(0, fm.fpioa.GPIOHS1, force=True)
            M1wPower=GPIO(GPIO.GPIOHS1, GPIO.OUT)
            M1wPower.value(0)
            # 注册WIFI使能管脚
            fm.register(8, fm.fpioa.GPIOHS0, force=True)
            WiFiEn=GPIO(GPIO.GPIOHS0, GPIO.OUT)
            WiFiEn.value(0)
            time.sleep_ms(200)
            WiFiEn.value(1)
            time.sleep_ms(1000)
            # 初始化WIFI通讯串口
            fm.register(7, fm.fpioa.UART2_TX, force=True)
            fm.register(6, fm.fpioa.UART2_RX, force=True)
            uart = UART(UART.UART2, 115200, timeout=1000, read_buf_len=4096)
            # 对象绑定
            nic = network.ESP8285(uart)
            print("初始化WiFi硬件成功！！！\r\n")
            # 标记状态
            initWiFiState = 1
            # 改变结果
            initWiFiresult = True
            # 启动自动回收(如果出现网络中断，并尝试抵达初始化WiFi硬件时，开启自动回收)
            gc.enable()
    except Exception as e:
        print("初始化WiFi硬件发生错误：%s\r\n" % e)
        # 标记状态
        initWiFiState = 0

# 初始WIFI配置######################################################################################
#WiFiConfigInfoArray = ["HDDZ2G7","88888888"]
WiFiConfigInfoArray = [0,0]
WiFiConfigInfoJson = "{0,0}"
#ip = '***********'
#ip = '************'
ip = '************'
#ip = '************'

# WIFI配置状态
WiFiConfigState = 0
def WiFiConfig(boolean):
    # 标记全局变量
    global WiFiConfigInfoArray, WiFiConfigInfoJson, WiFiConfigState
    # 赋值信息
    WiFiConfigInfoJson = ujson.dumps({"SSID":WiFiConfigInfoArray[0],"PASSWD":WiFiConfigInfoArray[1]})
    # WiFi配置信息
    if boolean == True:
        try:
            print("正在写入WIFI配置到FLASH，写入信息为：%s\r\n" % WiFiConfigInfoJson)
            time.sleep_ms(1000)
            # 打开WiFi文件
            filesW = open('./WiFi.json', 'w',encoding = 'utf-8' )
            # 尝试写入
            filesW.write(WiFiConfigInfoJson)
            time.sleep_ms(200)
            # 关闭写入文件
            filesW.close()
            # 标记状态
            WiFiConfigState = 3
        except Exception as e:
            print("写入WiFi配置文件发生错误：%s\r\n" % e)
            # 标记状态
            WiFiConfigState = 4
    else:
        try:
            print('>>> 读取WiFi配置文件"WiFi.json"，开始寻找文件...')
            print(uos.listdir("./"))
            # 尝试打开WiFi配置配置文件
            filesR = open('./WiFi.json', 'r',encoding = 'utf-8' )
            WiFiConfigInfoJson = filesR.readline()
            printInfo = WiFiConfigInfoJson,len(WiFiConfigInfoJson), id(WiFiConfigInfoJson)
            print("成功读取WiFi配置文件，内容为：%s，文件长度：%i，内存地址：%i\r\n" % printInfo)
            # 判断读文件内容长度，如果空，则进入初始化配置文件
            if len(WiFiConfigInfoJson) == 0:
                print("读取读取WiFi配置信息错误，进行文件初始化...\r\n")
                # 关闭读取文件
                filesR.close()
                # 删除文件
                uos.remove('./WiFi.json')
                print("删除WIFI配置文件成功！！！\r\n")
                # 标记状态
                WiFiConfigState = 2
            else:
                # 获取并赋值配置信息
                WiFiConfigInfoJson = ujson.loads(WiFiConfigInfoJson)
                WiFiConfigInfoArray = [WiFiConfigInfoJson["SSID"],WiFiConfigInfoJson["PASSWD"]]
                # 标记状态
                WiFiConfigState = 1
            # 关闭打开的文件
            filesR.close()
        except Exception as e:
            print("读取WiFi配置文件发生错误：%s\r\n" % e)
            # 标记状态
            WiFiConfigState = 0

# 连接WiFi##########################################################################################
# 连接错误计数
connectWiFiErrCount = 0
# 状态标记
connectWiFiState = 0
# 累计连接错误
socketErrCount = 0
def connectWiFi():
    global connectWiFiErrCount, connectWiFiState, socketErrCount
    ssid = WiFiConfigInfoArray[0]
    security = WiFiConfigInfoArray[1]

    try:
        # 如果存在连接则断开
        if nic.isconnected() == True:
            print(">>> 释放WiFi连接...\r\n")
            nic.disconnect()
            # 关闭WiFi-LED
            LED1.value(1)
            # 标记状态
            connectWiFiState = 0
        else:
            print(">>> 正在连接WiFi...\r\n")
            # 开始连接
            nic.connect(ssid, security)
            time.sleep_ms(1000)
            print("WiFi连接成功！！！")
            # WiFi-LED
            LED1.value(0)
            # 标记状态
            connectWiFiState = 1
            # 初始化socket连接错误
            socketErrCount = 0
    except Exception as e:
        print("连接WiFi发生错误：%s\r\n" % e)
        # 标记状态
        connectWiFiState = 0
        # 关闭WiFi-LED
        LED1.value(1)
        # 如果WIFI连接错误大于等于3次，从新初始化WiFi硬件
        connectWiFiErrCount += 1
        if connectWiFiErrCount >= 3:
            connectWiFiErrCount = 0
            # 标记状态
            connectWiFiState = 2

# 创建socket对象并连接#################################################################################

socketClient = None
port = 8611
# 连接状态标记
createSocketObjConnectState = 0
# 心跳累计
heartbeatStatistics = 0
def createSocketObjConnect():
    global socketClient, socketErrCount, createSocketObjConnectState, heartbeatStatistics
    # 报送成功恢复报送错误
    statusSubmissionErr = 0
    try:
        print("创建socke对象...")
        # 创建socket对象
        socketClient = socket.socket()
        socketClient.settimeout(5.0)
        # 连接socket服务器
        # addr = socket.getaddrinfo(ip, port)[0][-1]
        # socketClient.connect(addr)
        socketClient.connect((ip, port))
        time.sleep_ms(1000)
        # 标记状态
        createSocketObjConnectState = 1
        #心跳累计初始化
        heartbeatStatistics = 0
    except Exception as e:
        print("创建socke对象发生错误：%s\r\n" % e)
        # 关闭socket-LED
        LED2.value(1)
        #改变运行状态
        runState = 6
        # 标记状态
        createSocketObjConnectState = 0
        # 如果socket连接错误大于等于3次，从新连接WiFi
        socketErrCount += 1
        if socketErrCount >= 3:
            createSocketObjConnectState = 2

# 取中间文本####################################################################################
def getmidstring(data, start_str, end):
    start = data.find(start_str)
    if start >= 0:
        start += len(start_str)
        end = data.find(end, start)
        if end >= 0:
            return data[start:end].strip()

# 精简射击数据，去掉末尾的'*'占位符##################################################################
def compactShootingData(shootingArray):
    """
    精简射击数据数组，去掉末尾的'*'占位符
    参数: shootingArray - 包含坐标数据和'*'占位符的数组
    返回: 只包含有效坐标数据的数组
    """
    compactArray = []
    for item in shootingArray:
        if item != '*':
            compactArray.append(item)
        else:
            break  # 遇到第一个'*'就停止，因为后面都是占位符
    return compactArray


#处理socket接收数据
def withSocketData():
    # 标记全局变量
    global nic,socketClient,WiFiConfigInfoArray,runState
    try:
        #尝试接收数据。
        data = socketClient.readline()
        if len(data) != 0:
            socketData = data.decode('utf-8')
            print("接收到的消息:%s\r\n" % socketData)

            #接收到服务器相应
            if socketData == '@state:{"result":"ok"};\r\n':
                # 标记全局变量
                global createSocketObjConnectState, heartbeatStatistics
                # 标记状态
                createSocketObjConnectState = 1
                # 点亮socketLED
                LED1.value(0)
                # 初始化心跳累计
                heartbeatStatistics = 0

            #接收到校准数据请求
            if socketData == '@apply:{"type":"calibration"};\r\n':
                try:
                    print("发送校准数据：@calibration:%s;\r\n" % calibrationJson)
                    socketClient.write("@calibration:" + ujson.dumps(calibrationJson) + ";\r\n")
                except Exception as e:
                    print("发送校准数据发生错误：%s;\r\n" % e)

            #接收修改SSID申请
            WiFiEditData = getmidstring(socketData,'@apply:{"type":"WiFiEdit","SSID":"HDDZ2G7','"};')
            if WiFiEditData:
                print("修改基站频点信息：%s;\r\n" % WiFiEditData)
                # 停止定时器
                tim.stop()
                # 释放socket
                LED1.value(1)
                socketClient.close()
                nic.disconnect()
                # 恢复WIFI配置信息
                WiFiConfigInfoArray = ["HDDZ2G7" + WiFiEditData,"88888888"]
                # 初始化WIFI连接信息到出厂
                print("正在修改基站频点...")
                WiFiConfig(True)
                # 重新初始化WIFI硬件
                runState = 3


            #接收到关机请求
            if socketData == '@apply:{"type":"shutDown"};\r\n':
                print("收到靶面关机指令，开始关机！")
                # 停止定时器
                tim.stop()
                PowerKey.value(0)
                time.sleep_ms(500)
                PowerKey.value(1)
                time.sleep_ms(500)
                PowerKey.value(0)
                time.sleep_ms(500)
                PowerKey.value(1)

                #LED控制计数
                counter = 0
                while (True):
                    counter += 1
                    if counter == 1:
                        LED0.value(0)
                        LED1.value(1)
                        LED2.value(1)

                    if counter == 2:
                        LED0.value(1)
                        LED1.value(0)
                        LED2.value(1)

                    if counter == 3:
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(0)
                        counter = 0

                    time.sleep_ms(200)

    except Exception as e:
        print("接收socket数据出现错误:%s\r\n" % e)

# 心跳####################################################################################

# 系统运行步骤
runState = 0
# 初始化心跳时钟
clockTime = time.ticks_ms()

# 进入循环时间标记
clock = time.clock()

# 校准步骤
calibrationStep = 0

# 定义要采集的颜色(高光白色)
thresholds = [(240, 255)]

# WIFI连接错误累计
connectWiFiErrCount = 0

# 干扰状态
interfereState = False
# 干扰状态控制时钟
soClock1 = 0


# 预瞄和射击数组指针
shootingCount = 0
# 预瞄和射击结束数据（每秒60帧，5秒）
shootingArray = ['*'] * 150

#业务逻辑循环
while(True):

    # 更新FPS时钟。
    clock.tick()

    # 初始化IO
    if runState == 0:
        lampAndButton(False)
        if lampAndButtonState == 1:
            runState = 1

    # 初始化摄像头
    if runState == 1:
        cameraReset(False)
        if cameraResetState == 1:
            runState = 2

    # 按下了校准按钮，进入校准模式，否则初始化WiFi
    if runState == 2 and KEY0.value() == 0:
        # 恢复WIFI配置信息
        WiFiConfigInfoArray = ["HDDZ2G9","88888888"]
        # 初始化校准步骤
        calibrationStep = 0
        # 初始化WIFI连接信息到出厂
        print("正在恢复WiFi配置信息到出厂状态...")
        WiFiConfig(True)
        if WiFiConfigState == 3:
            print(">>> 进入校准模式成功...\r\n")
            runState = 9

    # 执行校准函数
    if runState == 2 and KEY0.value() == 1:
        calibration(False)

        # 读取发生错误
        if calibrationState == 0:
            calibration(True)

        # 操作成功
        if calibrationState == 1:
            runState = 3

        # 没有初始化
        if calibrationState == 2:
            print("没有出厂校准数据！！！\r\n")
            # 系统状态LED提示
            LedState = 0
            #LED控制计数
            counter = 0
            while counter <= 5:
                counter += 1
                if LedState == 0:
                    LED0.value(0)
                    LED1.value(1)
                    LED2.value(0)
                    LedState = 1
                else:
                    LED0.value(1)
                    LED1.value(0)
                    LED2.value(1)
                    LedState = 0
                time.sleep_ms(200)

    # 初始化WiFi硬件对象
    if runState == 3:
        initWiFi()

        # 读取WiFi配置文件发生了错误，从新初始化
        if initWiFiState == 0:
            initWiFi()

        # 初始化成功
        if initWiFiState == 1:
            runState = 4

    # 读取FLASH中的WiFi配置信息
    if runState == 4:
        WiFiConfig(False)

        # 读取发生错误，从新初始化
        if WiFiConfigState == 0:
            WiFiConfig(True)

        # 读取成功
        if WiFiConfigState == 1:
            runState = 5

        # 内容文件错误
        if WiFiConfigState == 2:
            WiFiConfig(True)

        # 写入错误
        if WiFiConfigState == 4:
            # 系统状态LED提示
            LedState = 0
            #LED控制计数
            counter = 0
            while counter <= 5:
                counter += 1
                if LedState == 0:
                    LED0.value(0)
                    LED1.value(1)
                    LED2.value(0)
                    LedState = 1
                else:
                    LED0.value(1)
                    LED1.value(1)
                    LED2.value(1)
                    LedState = 0
                time.sleep_ms(200)

    # 连接WiFi
    if runState == 5:
        connectWiFi()

        # 连接成功
        if connectWiFiState == 1:
           runState = 6

        # 重连超过阀值
        if connectWiFiState == 2:
           # 重启WiFi硬件
           runState = 3

    # 创建Socke对象并连接
    if runState == 6:
        createSocketObjConnect()

        # 连接成功
        if createSocketObjConnectState == 1:
           try:
                time.sleep_ms(5000)
                socketClient.write("@calibration:" + ujson.dumps(calibrationJson) + ";\r\n")
                print("发送校准数据：@calibration:%s;\r\n" % calibrationJson)
                runState = 7
           except Exception as e:
                print("发送校准数据发生错误：%s;\r\n" % e)


        # 重连超过阀值
        if createSocketObjConnectState == 2:
           # 重连WiFi
           runState = 5



    # 射击模式########################################################################################
    if runState == 7:

        # 关闭自动回收，转为手动回收（发现自动回收长时间运行导致未知情况的报错停止）
        #gc.disable()

        # 接收socket数据，该模式会阻塞进程
        #withSocketData()

        # 报送靶面状态####################################################################################
        if time.ticks_ms() - clockTime > 3000:
            clockTime = time.ticks_ms()
            try:
                # 开始报送
                head_info = '@state:{"target":1};\r\n'
                socketClient.send(head_info.encode('utf-8'))
                # 点亮socket-LED
                LED2.value(0)
                print("报送靶面状态:%s\r\n" % head_info)
            except Exception as e:
                # 关闭socket-LED
                LED2.value(1)
                # 释放可能的socket对象
                socketClient.close()
                #清空socket对象
                socketClient = None
                # 改变运行状态
                runState = 6
                print("报送靶面状态错误:%s\r\n" % e)

        # 取得当前图像并返回
        img = sensor.snapshot()

        # 取得图像符合条件的追中信息
        blobs = img.find_blobs([thresholds[0]], merge=True)

        #处理识别信息
        if blobs:

            # 过滤条件
            if (blobs[0].w()>=1 and blobs[0].w()<=40) and (blobs[0].h()>=1 and blobs[0].h()<=40) and (blobs[0].pixels()>=10 and blobs[0].pixels()<=700):
                img.draw_rectangle(blobs[0].x(),blobs[0].y(),blobs[0].w(),blobs[0].h(), color=(255,0,0))
                img.draw_cross(blobs[0].cx(), blobs[0].cy(), color=(255,0,0))
                shootingArray[shootingCount] = [blobs[0].cx(),blobs[0].cy()]
                shootingCount += 1
                # 数据溢出,从新统计轨迹
                if shootingCount >= 148 :
                    shootingArray = ['*'] * 150
                    shootingCount = 0
                    #try:
                        ## 正在报送射击数据
                        #print("射击信息：@optics:%s;\r\n" % shootingArray)
                        #socketClient.send("@optics:" + ujson.dumps(shootingArray) + ";\r\n")
                    #except Exception as e:
                        #print("处理射击信息错误：%s\r\n" % e)
                    #time.sleep_ms(2000)
                #没有干扰数据
                interfereState = False
                print("追光信息：%s\r\n" % blobs[0])
            else:
                # 产生干扰数据
                interfereState = True
                print("干扰数据:%s\r\n" % blobs[0])
        else:
            # 没有干扰数据
            interfereState = False
            # 初始化射击数据指针
            shootingCount = 0
            # 判断射击数据是否符合条件
            #if shootingArray[0] != '*' and shootingArray[1] != '*' and shootingArray[2] != '*' and shootingArray[3] != '*':
            if shootingArray[0] != '*' and shootingArray[1] != '*':
                try:
                    # 精简射击数据，去掉末尾的'*'占位符
                    compactData = compactShootingData(shootingArray)
                    # 正在报送射击数据
                    time.sleep_ms(250)
                    socketClient.write("@optics:" + ujson.dumps(compactData) + ";\r\n")
                    socketClient.settimeout(1.0)
                    print("射击信息：%s\r\n" % compactData)

                except Exception as e:
                    print("处理射击信息错误：%s\r\n" % e)

                print("射击信息：@optics:%s;\r\n" % compactData)

            # 射击数据初始化
            shootingArray = ['*'] * 150

        # 如果产生干扰，调整LED状态
        if interfereState == True:
            soClock1 += 1
            if soClock1 == 5:
                LED2.value(1)

            if soClock1 == 10:
                LED2.value(0)

            if soClock1 == 15:
                soClock1 = 0
                LED2.value(1)
        else:
            soClock1 = 0
            LED2.value(0)

        # 打印系统信息
        try:
            systemInfo = clock.fps(),freq.get_cpu(),freq.get_kpu(),gc.mem_free()/1024,gc.mem_alloc()/1024,runState,heartbeatStatistics
            #print("FPS: %10.2f S,CPU: %10.2f M,KPU: %10.2f M,RAM:%10.2f/%-10.2fK,runState:%s,heartbeat:%s" % systemInfo)
        except Exception as e:
            print("监视帧率出错:%s\r\n" % e)

        ## 释放内存
        #if gc.mem_free()/1020 <= 128:
            #gc.collect()

    # 校准模式########################################################################################
    if runState == 9:

        # 中心
        if calibrationStep == 0:
            LED0.value(1)
            LED1.value(0)
            LED2.value(1)
        # 左
        if calibrationStep == 1:
            # 改变LED状态
            LED0.value(0)
            LED1.value(1)
            LED2.value(1)
        # 右
        if calibrationStep == 2:
            # 改变LED状态
            LED0.value(1)
            LED1.value(1)
            LED2.value(0)
        # 上
        if calibrationStep == 3:
            # 改变LED状态
            LED0.value(0)
            LED1.value(0)
            LED2.value(1)
        # 下
        if calibrationStep == 4:
            # 改变LED状态
            LED0.value(1)
            LED1.value(0)
            LED2.value(0)

        # 开始处理图像
        try:
            # 取得当前图像并返回
            img = sensor.snapshot()
            blobs = img.find_blobs([thresholds[0]], merge=True)

            #处理识别信息
            if blobs:
                # 过滤条件
                if (blobs[0].w()>=1 and blobs[0].w()<=40) and (blobs[0].h()>=1 and blobs[0].h()<=40) and (blobs[0].pixels()>=10 and blobs[0].pixels()<=700):
                    img.draw_rectangle(blobs[0].x(),blobs[0].y(),blobs[0].w(),blobs[0].h(), color=(255,0,0))
                    img.draw_cross(blobs[0].cx(), blobs[0].cy(), color=(255,0,0))
                    shootingArray[shootingCount] = [blobs[0].cx(),blobs[0].cy()]
                    # 校准步骤计数（主要用于检测校准光是否稳定，累加60个为完成）
                    calibrationStepCount += 1
                    # 校准中心点
                    if calibrationStepCount >= 60 and calibrationStep == 0:
                        calibrationStep = 1
                        calibrationStepCount = 0
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(1)
                        # 中心点数据
                        calibrationArray = [blobs[0].x(),blobs[0].y(),0,0,0,0]
                        calibrationInfo = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})
                        print("校准数信息：%s\r\n" % calibrationInfo)
                        time.sleep_ms(2000)


                    # 校准左边界
                    if calibrationStepCount >= 60 and calibrationStep == 1:
                        calibrationStep = 2
                        calibrationStepCount = 0
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(1)
                        # 左边界数据
                        calibrationArray[2] = blobs[0].x()
                        calibrationInfo = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})
                        print("校准数信息：%s\r\n" % calibrationInfo)
                        time.sleep_ms(2000)

                    # 校准右边界
                    if calibrationStepCount >= 60 and calibrationStep == 2:
                        calibrationStep = 3
                        calibrationStepCount = 0
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(1)
                        # 右边界数据
                        calibrationArray[3] = blobs[0].x()
                        calibrationInfo = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})
                        print("校准数信息：%s\r\n" % calibrationInfo)
                        time.sleep_ms(2000)

                    # 校准上边界
                    if calibrationStepCount >= 60 and calibrationStep == 3:
                        calibrationStep = 4
                        calibrationStepCount = 0
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(1)
                        # 右边界数据
                        calibrationArray[4] = blobs[0].y()
                        calibrationInfo = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})
                        print("校准数信息：%s\r\n" % calibrationInfo)
                        time.sleep_ms(2000)

                    # 校准下边界
                    if calibrationStepCount >= 60 and calibrationStep == 4:
                        calibrationStep = 5
                        calibrationStepCount = 0
                        LED0.value(1)
                        LED1.value(1)
                        LED2.value(1)
                        # 右边界数据
                        calibrationArray[5] = blobs[0].y()
                        calibrationInfo = ujson.dumps({"Xcenter":calibrationArray[0],"Ycenter":calibrationArray[1],"XSmall":calibrationArray[2],"XLarge":calibrationArray[3],"YSmall":calibrationArray[4],"YLarge":calibrationArray[5]})
                        print("校准数信息：%s\r\n" % calibrationInfo)
                        time.sleep_ms(2000)

                    # 校准结束
                    if calibrationStep == 5:
                        calibrationStep = 6
                        # 将校准数据写入到FLASH
                        # 初始化校准状态
                        calibrationState = 0
                        calibration(True)
                        if calibrationState == 1:
                            print(">>> 校准模式结束，请重新启动设备...\r\n")
                            # 系统状态LED提示
                            #LED控制
                            LedState = 0
                            #LED控制计数
                            counter = 0
                            while(True):
                                counter += 1
                                if LedState == 0:
                                    LED0.value(0)
                                    LED1.value(0)
                                    LED2.value(0)
                                    LedState = 1
                                else:
                                    LED0.value(1)
                                    LED1.value(1)
                                    LED2.value(1)
                                    LedState = 0
                                time.sleep_ms(200)

                    # 输出步骤信息
                    if calibrationStep <= 4:
                        print(blobs)
                        stepInfo = calibrationStepCount,calibrationStep
                        print("校准步骤计数：%i，校准步骤：%i\r\n" % stepInfo)

        except Exception as e:
            print("校准模式发生错误:%s\r\n" % e)
            time.sleep_ms(2000)
